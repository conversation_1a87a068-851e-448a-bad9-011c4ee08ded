import React, { useState, useEffect } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { ArrowLeft, Save, Send, Calendar, Users, FileText } from 'lucide-react'
import { api } from '../services/api'

interface Template {
  id: number
  name: string
  description: string
}

interface ContactList {
  id: number
  name: string
  count: number
}

interface CampaignData {
  name: string
  subject: string
  template_id: string
  recipient_lists: number[]
  scheduled_at: string
  send_immediately: boolean
}

const CampaignCreator: React.FC = () => {
  const navigate = useNavigate()
  const { id } = useParams()
  const isEditing = Boolean(id)

  const [currentStep, setCurrentStep] = useState(1)
  const [campaignData, setCampaignData] = useState<CampaignData>({
    name: '',
    subject: '',
    template_id: '',
    recipient_lists: [],
    scheduled_at: '',
    send_immediately: true,
  })
  const [templates, setTemplates] = useState<Template[]>([])
  const [contactLists, setContactLists] = useState<ContactList[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const steps = [
    { id: 1, name: 'Campaign Details', icon: FileText },
    { id: 2, name: 'Select Template', icon: FileText },
    { id: 3, name: 'Choose Recipients', icon: Users },
    { id: 4, name: 'Schedule & Send', icon: Calendar },
  ]

  useEffect(() => {
    fetchTemplates()
    fetchContacts()
    if (isEditing && id) {
      fetchCampaign(id)
    }
  }, [isEditing, id])

  const fetchTemplates = async () => {
    try {
      const templates = await api.get<any[]>('/templates')
      setTemplates(templates.map((template: any) => ({
        id: template.id,
        name: template.name,
        description: template.description || 'Email template'
      })))
    } catch (err) {
      console.error('Failed to fetch templates:', err)
      setError('Failed to load templates')
    }
  }

  const fetchContacts = async () => {
    try {
      const contacts = await api.get<any[]>('/contacts')
      // Group contacts by status and create mock lists for now
      const activeContacts = contacts.filter((contact: any) => contact.status === 'active')
      setContactLists([
        { id: 1, name: 'All Active Contacts', count: activeContacts.length },
        { id: 2, name: 'Recent Subscribers', count: Math.floor(activeContacts.length * 0.3) },
        { id: 3, name: 'Engaged Users', count: Math.floor(activeContacts.length * 0.6) },
      ])
    } catch (err) {
      console.error('Failed to fetch contacts:', err)
      setError('Failed to load contacts')
    }
  }

  const fetchCampaign = async (campaignId: string) => {
    try {
      const campaign = await api.get<any>(`/campaigns/${campaignId}`)
      setCampaignData({
        name: campaign.name,
        subject: campaign.subject,
        template_id: campaign.template_id.toString(),
        recipient_lists: [], // Will be populated from campaign recipients
        scheduled_at: campaign.scheduled_at || '',
        send_immediately: !campaign.scheduled_at,
      })
    } catch (err) {
      console.error('Failed to fetch campaign:', err)
      setError('Failed to load campaign')
    }
  }

  const handleNext = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSave = async () => {
    try {
      setLoading(true)
      setError(null)

      const payload = {
        ...campaignData,
        status: 'draft',
        template_id: parseInt(campaignData.template_id),
        scheduled_at: campaignData.send_immediately ? null : campaignData.scheduled_at
      }

      if (isEditing && id) {
        await api.put(`/campaigns/${id}`, payload)
      } else {
        await api.post('/campaigns', payload)
      }

      navigate('/campaigns')
    } catch (err: any) {
      console.error('Failed to save campaign:', err)
      setError(err.response?.data?.message || 'Failed to save campaign')
    } finally {
      setLoading(false)
    }
  }

  const handleSend = async () => {
    try {
      setLoading(true)
      setError(null)

      const payload = {
        ...campaignData,
        status: campaignData.send_immediately ? 'sending' : 'scheduled',
        template_id: parseInt(campaignData.template_id),
        scheduled_at: campaignData.send_immediately ? null : campaignData.scheduled_at
      }

      let campaignId = id
      if (!isEditing) {
        // Create campaign first
        const campaign = await api.post<any>('/campaigns', payload)
        campaignId = campaign.id
      } else {
        await api.put(`/campaigns/${id}`, payload)
      }

      if (campaignData.send_immediately && campaignId) {
        // Send immediately
        await api.post(`/campaigns/${campaignId}/send`)
      }

      navigate('/campaigns')
    } catch (err: any) {
      console.error('Failed to send campaign:', err)
      setError(err.response?.data?.message || 'Failed to send campaign')
    } finally {
      setLoading(false)
    }
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Campaign Name
              </label>
              <input
                type="text"
                value={campaignData.name}
                onChange={(e) => setCampaignData({ ...campaignData, name: e.target.value })}
                className="input"
                placeholder="Enter campaign name"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Subject
              </label>
              <input
                type="text"
                value={campaignData.subject}
                onChange={(e) => setCampaignData({ ...campaignData, subject: e.target.value })}
                className="input"
                placeholder="Enter email subject line"
              />
            </div>
          </div>
        )

      case 2:
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Choose a Template</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {templates.map((template) => (
                <div
                  key={template.id}
                  className={`card p-4 cursor-pointer transition-colors ${
                    campaignData.template_id === template.id.toString()
                      ? 'ring-2 ring-primary-500 bg-primary-50'
                      : 'hover:bg-gray-50'
                  }`}
                  onClick={() => setCampaignData({ ...campaignData, template_id: template.id.toString() })}
                >
                  <div className="h-32 bg-gray-100 rounded-lg mb-3 flex items-center justify-center">
                    <FileText className="w-8 h-8 text-gray-400" />
                  </div>
                  <h4 className="font-medium text-gray-900">{template.name}</h4>
                  <p className="text-sm text-gray-600">{template.description}</p>
                </div>
              ))}
            </div>
          </div>
        )

      case 3:
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Select Recipients</h3>
            <div className="space-y-3">
              {contactLists.map((list) => (
                <div
                  key={list.id}
                  className="flex items-center p-4 border border-gray-200 rounded-lg"
                >
                  <input
                    type="checkbox"
                    id={`list-${list.id}`}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    checked={campaignData.recipient_lists.includes(list.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setCampaignData({
                          ...campaignData,
                          recipient_lists: [...campaignData.recipient_lists, list.id]
                        })
                      } else {
                        setCampaignData({
                          ...campaignData,
                          recipient_lists: campaignData.recipient_lists.filter(id => id !== list.id)
                        })
                      }
                    }}
                  />
                  <label htmlFor={`list-${list.id}`} className="ml-3 flex-1">
                    <div className="font-medium text-gray-900">{list.name}</div>
                    <div className="text-sm text-gray-600">{list.count} contacts</div>
                  </label>
                </div>
              ))}
            </div>
          </div>
        )

      case 4:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900">Schedule & Send</h3>
            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  type="radio"
                  id="send-now"
                  name="send-timing"
                  checked={campaignData.send_immediately}
                  onChange={() => setCampaignData({ ...campaignData, send_immediately: true })}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                />
                <label htmlFor="send-now" className="ml-3 text-sm font-medium text-gray-700">
                  Send immediately
                </label>
              </div>
              <div className="flex items-center">
                <input
                  type="radio"
                  id="schedule"
                  name="send-timing"
                  checked={!campaignData.send_immediately}
                  onChange={() => setCampaignData({ ...campaignData, send_immediately: false })}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                />
                <label htmlFor="schedule" className="ml-3 text-sm font-medium text-gray-700">
                  Schedule for later
                </label>
              </div>
              {!campaignData.send_immediately && (
                <div className="ml-7">
                  <input
                    type="datetime-local"
                    value={campaignData.scheduled_at}
                    onChange={(e) => setCampaignData({ ...campaignData, scheduled_at: e.target.value })}
                    className="input"
                  />
                </div>
              )}
            </div>

            {/* Campaign Summary */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-3">Campaign Summary</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Campaign Name:</span>
                  <span className="font-medium">{campaignData.name || 'Untitled Campaign'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Subject:</span>
                  <span className="font-medium">{campaignData.subject || 'No subject'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Recipients:</span>
                  <span className="font-medium">
                    {campaignData.recipient_lists.reduce((total, listId) => {
                      const list = contactLists.find(l => l.id === listId)
                      return total + (list?.count || 0)
                    }, 0)} contacts
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Send Time:</span>
                  <span className="font-medium">
                    {campaignData.send_immediately ? 'Immediately' : campaignData.scheduled_at || 'Not scheduled'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate('/campaigns')}
              className="btn btn-outline p-2"
            >
              <ArrowLeft className="w-4 h-4" />
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {isEditing ? 'Edit Campaign' : 'Create Campaign'}
              </h1>
              <p className="text-gray-600">
                Step {currentStep} of {steps.length}
              </p>
            </div>
          </div>
        </div>

        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div
                  className={`flex items-center justify-center w-10 h-10 rounded-full ${
                    currentStep >= step.id
                      ? 'bg-primary-600 text-white'
                      : 'bg-gray-200 text-gray-600'
                  }`}
                >
                  <step.icon className="w-5 h-5" />
                </div>
                <div className="ml-3">
                  <div className={`text-sm font-medium ${
                    currentStep >= step.id ? 'text-primary-600' : 'text-gray-500'
                  }`}>
                    {step.name}
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-16 h-0.5 mx-4 ${
                    currentStep > step.id ? 'bg-primary-600' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Step Content */}
        <div className="card p-6 mb-8">
          {renderStepContent()}
        </div>

        {/* Navigation */}
        <div className="flex items-center justify-between">
          <button
            onClick={handlePrevious}
            disabled={currentStep === 1}
            className="btn btn-outline disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          <div className="flex items-center space-x-3">
            <button onClick={handleSave} className="btn btn-secondary">
              <Save className="w-4 h-4 mr-2" />
              Save Draft
            </button>
            {currentStep === steps.length ? (
              <button onClick={handleSend} className="btn btn-primary">
                <Send className="w-4 h-4 mr-2" />
                {campaignData.send_immediately ? 'Send Campaign' : 'Schedule Campaign'}
              </button>
            ) : (
              <button onClick={handleNext} className="btn btn-primary">
                Next
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default CampaignCreator
