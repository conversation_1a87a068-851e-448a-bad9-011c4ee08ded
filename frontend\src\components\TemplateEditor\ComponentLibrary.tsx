import React, { useState } from 'react'
import { useDrag } from 'react-dnd'
import { 
  Type, 
  Image, 
  MousePointer, 
  Minus, 
  Square, 
  Layout,
  Mail,
  Star,
  ShoppingCart,
  Users,
  Calendar,
  ChevronDown,
  ChevronRight
} from 'lucide-react'

interface ComponentLibraryProps {
  onComponentAdd: (component: any) => void
}

interface ComponentCategory {
  id: string
  name: string
  icon: React.ReactNode
  components: ComponentDefinition[]
}

interface ComponentDefinition {
  id: string
  name: string
  icon: React.ReactNode
  componentType: string
  defaultContent: any
  defaultStyles: any
  preview: string
}

const DraggableComponentItem: React.FC<{
  component: ComponentDefinition
  onAdd: () => void
}> = ({ component, onAdd }) => {
  const [{ isDragging }, drag] = useDrag({
    type: 'component',
    item: {
      type: 'component',
      componentType: component.componentType,
      defaultContent: component.defaultContent,
      defaultStyles: component.defaultStyles
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging()
    })
  })

  return (
    <div
      ref={drag as any}
      className={`p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors ${
        isDragging ? 'opacity-50' : ''
      }`}
      onClick={onAdd}
    >
      <div className="flex items-center space-x-2 mb-2">
        {component.icon}
        <span className="text-sm font-medium text-gray-700">{component.name}</span>
      </div>
      <div 
        className="text-xs text-gray-500 bg-gray-100 p-2 rounded"
        dangerouslySetInnerHTML={{ __html: component.preview }}
      />
    </div>
  )
}

export const ComponentLibrary: React.FC<ComponentLibraryProps> = ({ onComponentAdd }) => {
  const [expandedCategories, setExpandedCategories] = useState<string[]>(['basic', 'layout'])

  const toggleCategory = (categoryId: string) => {
    setExpandedCategories(prev => 
      prev.includes(categoryId) 
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    )
  }

  const componentCategories: ComponentCategory[] = [
    {
      id: 'basic',
      name: 'Basic Elements',
      icon: <Type className="w-4 h-4" />,
      components: [
        {
          id: 'text',
          name: 'Text Block',
          icon: <Type className="w-4 h-4 text-blue-600" />,
          componentType: 'text',
          defaultContent: { text: 'Your text here...' },
          defaultStyles: {
            fontSize: '16px',
            color: '#374151',
            fontFamily: 'Arial, sans-serif',
            lineHeight: '1.5',
            padding: '10px'
          },
          preview: '<p style="font-size: 14px; margin: 0;">Your text here...</p>'
        },
        {
          id: 'heading',
          name: 'Heading',
          icon: <Type className="w-4 h-4 text-purple-600" />,
          componentType: 'heading',
          defaultContent: { text: 'Your Heading', level: 'h2' },
          defaultStyles: {
            fontSize: '24px',
            color: '#1f2937',
            fontFamily: 'Arial, sans-serif',
            fontWeight: 'bold',
            padding: '10px'
          },
          preview: '<h3 style="font-size: 16px; margin: 0; font-weight: bold;">Your Heading</h3>'
        },
        {
          id: 'image',
          name: 'Image',
          icon: <Image className="w-4 h-4 text-green-600" />,
          componentType: 'image',
          defaultContent: { 
            src: 'https://via.placeholder.com/300x200', 
            alt: 'Image description' 
          },
          defaultStyles: {
            width: '300px',
            height: '200px',
            borderRadius: '4px'
          },
          preview: '<div style="width: 60px; height: 40px; background: #e5e7eb; border-radius: 2px;"></div>'
        },
        {
          id: 'button',
          name: 'Button',
          icon: <MousePointer className="w-4 h-4 text-red-600" />,
          componentType: 'button',
          defaultContent: { text: 'Click Here', href: '#' },
          defaultStyles: {
            backgroundColor: '#3b82f6',
            color: '#ffffff',
            padding: '12px 24px',
            borderRadius: '6px',
            textDecoration: 'none',
            display: 'inline-block',
            fontWeight: '500'
          },
          preview: '<div style="background: #3b82f6; color: white; padding: 4px 8px; border-radius: 3px; font-size: 12px; text-align: center;">Click Here</div>'
        }
      ]
    },
    {
      id: 'layout',
      name: 'Layout Elements',
      icon: <Layout className="w-4 h-4" />,
      components: [
        {
          id: 'divider',
          name: 'Divider',
          icon: <Minus className="w-4 h-4 text-gray-600" />,
          componentType: 'divider',
          defaultContent: {},
          defaultStyles: {
            height: '1px',
            backgroundColor: '#e5e7eb',
            margin: '20px 0',
            width: '100%'
          },
          preview: '<div style="height: 1px; background: #e5e7eb; width: 100%;"></div>'
        },
        {
          id: 'spacer',
          name: 'Spacer',
          icon: <Square className="w-4 h-4 text-gray-600" />,
          componentType: 'spacer',
          defaultContent: {},
          defaultStyles: {
            height: '40px',
            width: '100%'
          },
          preview: '<div style="height: 20px; background: transparent; border: 1px dashed #d1d5db;"></div>'
        },
        {
          id: 'container',
          name: 'Container',
          icon: <Square className="w-4 h-4 text-indigo-600" />,
          componentType: 'container',
          defaultContent: {},
          defaultStyles: {
            padding: '20px',
            backgroundColor: '#f9fafb',
            borderRadius: '8px',
            border: '1px solid #e5e7eb'
          },
          preview: '<div style="background: #f9fafb; border: 1px solid #e5e7eb; padding: 8px; border-radius: 4px; font-size: 10px;">Container</div>'
        }
      ]
    },
    {
      id: 'content',
      name: 'Content Blocks',
      icon: <Mail className="w-4 h-4" />,
      components: [
        {
          id: 'header',
          name: 'Header',
          icon: <Layout className="w-4 h-4 text-blue-600" />,
          componentType: 'header',
          defaultContent: {
            logo: 'https://via.placeholder.com/120x40',
            title: 'Your Company'
          },
          defaultStyles: {
            backgroundColor: '#1f2937',
            color: '#ffffff',
            padding: '20px',
            textAlign: 'center'
          },
          preview: '<div style="background: #1f2937; color: white; padding: 8px; text-align: center; font-size: 10px;">Header</div>'
        },
        {
          id: 'footer',
          name: 'Footer',
          icon: <Layout className="w-4 h-4 text-gray-600" />,
          componentType: 'footer',
          defaultContent: {
            text: '© 2024 Your Company. All rights reserved.',
            links: []
          },
          defaultStyles: {
            backgroundColor: '#f3f4f6',
            color: '#6b7280',
            padding: '20px',
            textAlign: 'center',
            fontSize: '14px'
          },
          preview: '<div style="background: #f3f4f6; color: #6b7280; padding: 8px; text-align: center; font-size: 10px;">Footer</div>'
        },
        {
          id: 'product-card',
          name: 'Product Card',
          icon: <ShoppingCart className="w-4 h-4 text-green-600" />,
          componentType: 'product-card',
          defaultContent: {
            image: 'https://via.placeholder.com/200x200',
            title: 'Product Name',
            description: 'Product description here...',
            price: '$99.99',
            buttonText: 'Buy Now'
          },
          defaultStyles: {
            border: '1px solid #e5e7eb',
            borderRadius: '8px',
            padding: '16px',
            backgroundColor: '#ffffff'
          },
          preview: '<div style="border: 1px solid #e5e7eb; padding: 6px; border-radius: 4px; font-size: 10px;">Product Card</div>'
        },
        {
          id: 'social-media',
          name: 'Social Media',
          icon: <Users className="w-4 h-4 text-blue-500" />,
          componentType: 'social-media',
          defaultContent: {
            platforms: ['facebook', 'twitter', 'instagram', 'linkedin']
          },
          defaultStyles: {
            textAlign: 'center',
            padding: '20px'
          },
          preview: '<div style="text-align: center; font-size: 10px;">🔗 Social Links</div>'
        }
      ]
    }
  ]

  const handleComponentAdd = (component: ComponentDefinition) => {
    const newComponent = {
      id: `component-${Date.now()}`,
      type: component.componentType,
      content: component.defaultContent,
      styles: {
        ...component.defaultStyles,
        position: 'relative',
        zIndex: 1
      },
      position: { x: 0, y: 0 }
    }
    onComponentAdd(newComponent)
  }

  return (
    <div className="w-80 bg-white border-r border-gray-200 p-4 overflow-y-auto">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Components</h3>
      
      <div className="space-y-4">
        {componentCategories.map((category) => (
          <div key={category.id} className="border border-gray-200 rounded-lg">
            <button
              onClick={() => toggleCategory(category.id)}
              className="w-full flex items-center justify-between p-3 text-left hover:bg-gray-50"
            >
              <div className="flex items-center space-x-2">
                {category.icon}
                <span className="font-medium text-gray-700">{category.name}</span>
              </div>
              {expandedCategories.includes(category.id) ? (
                <ChevronDown className="w-4 h-4 text-gray-500" />
              ) : (
                <ChevronRight className="w-4 h-4 text-gray-500" />
              )}
            </button>
            
            {expandedCategories.includes(category.id) && (
              <div className="p-3 pt-0 space-y-3">
                {category.components.map((component) => (
                  <DraggableComponentItem
                    key={component.id}
                    component={component}
                    onAdd={() => handleComponentAdd(component)}
                  />
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}
